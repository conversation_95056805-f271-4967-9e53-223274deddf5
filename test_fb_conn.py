#!/usr/bin/env python3
# 测试Firebird连接模块

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(override=True)

# 设置测试环境
os.environ['APP_ENV'] = 'local_testing'

# 导入优化后的连接模块
from utils.basic.fb_conn import (
    get_firebird_config, 
    execute_pro2_query,
    LOCATION
)

def test_firebird_config():
    """测试Firebird配置获取"""
    print(f"当前地点配置: {LOCATION}")
    
    # 测试不同地点的配置
    locations = ['QD', 'SH', 'HK', 'TY']
    
    for location in locations:
        try:
            config = get_firebird_config(location)
            print(f"地点 {location} 配置:")
            print(f"  主机: {config['host']}")
            print(f"  端口: {config['port']}")
            print(f"  数据库路径: {config['db_path']}")
            print(f"  系统ID: {config['system_id']}")
            print()
        except Exception as e:
            print(f"地点 {location} 配置错误: {e}")
            print()

def test_firebird_query():
    """测试Firebird查询（模拟模式）"""
    try:
        # 测试单条查询
        result = execute_pro2_query("SELECT * FROM USERS WHERE ID = 1")
        print(f"单条查询结果: {result}")
        
        # 测试多条查询
        results = execute_pro2_query("SELECT * FROM USERS", fetch_all=True)
        print(f"多条查询结果: {results}")
        
        # 测试不同地点的查询
        result_sh = execute_pro2_query("SELECT * FROM USERS WHERE ID = 1", location='SH')
        print(f"上海地点查询结果: {result_sh}")
        
        print("✅ Firebird查询测试通过")
        
    except Exception as e:
        print(f"❌ Firebird查询测试失败: {e}")

if __name__ == "__main__":
    print("开始测试优化后的Firebird连接模块...")
    print("=" * 50)
    
    test_firebird_config()
    test_firebird_query()
    
    print("=" * 50)
    print("测试完成!")