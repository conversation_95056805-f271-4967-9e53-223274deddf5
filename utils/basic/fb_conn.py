# Firebird数据库连接模块 - 支持多地点配置

import os
import logging

import firebirdsql as fdb
from typing import List, Dict, Tuple, Union, Optional

from dotenv import load_dotenv
import threading
from queue import Queue, Empty
from contextlib import contextmanager
# from config import Config  # 暂时注释掉，避免导入错误

import time
import socket
import functools
from random import uniform

# 加载环境变量
load_dotenv(override=True)

# 设置日志级别 - 生产环境使用WARNING
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 重试装饰器
def retry_on_connection_error(max_attempts=3, delay=1.0, backoff=2.0):
    """
    连接重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避系数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except (BrokenPipeError, ConnectionError, 
                        fdb.DatabaseError, fdb.OperationalError,
                        socket.error, OSError) as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        # 对于recv相关错误，增加等待时间
                        if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                            wait_time = delay * (backoff ** attempt) + uniform(1.0, 2.0)  # 额外等待时间
                            logger.warning(f"[RETRY] {func.__name__} 网络接收错误 (尝试 {attempt + 1}/{max_attempts}): {e}, {wait_time:.2f}秒后重试")
                        else:
                            wait_time = delay * (backoff ** attempt) + uniform(0, 0.1)
                            logger.warning(f"[RETRY] {func.__name__} 连接失败 (尝试 {attempt + 1}/{max_attempts}): {e}, {wait_time:.2f}秒后重试")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"[RETRY] {func.__name__} 重试{max_attempts}次后仍然失败: {e}")
                        break
                except Exception as e:
                    # 非连接相关错误直接抛出
                    logger.error(f"[RETRY] {func.__name__} 非连接错误: {e}")
                    raise
            
            # 所有重试都失败了，抛出最后一个异常
            raise last_exception
        return wrapper
    return decorator

# Firebird数据库配置
FIREBIRD_USER = os.getenv("FIREBIRD_USER", "SYSDBA")
FIREBIRD_PASSWORD = os.getenv("FIREBIRD_PASSWORD", "masterkey")

# 获取当前运行地点
LOCATION = os.getenv("LOCATION", "QD")

def get_firebird_config(location: str = None):
    """
    根据地点参数获取Firebird配置
    Args:
        location: 地点代码，如 QD, SH, HK, TY等，如果为None则使用环境变量LOCATION
    """
    if location is None:
        location = LOCATION
    
    config = {
        'host': os.getenv(f"FB_HOST_{location}"),
        'port': int(os.getenv(f"FB_HOST_PORT_{location}", 3050)),
        'db_path': os.getenv(f"FD_DB_PATH_{location}"),
        'system_id': os.getenv(f"PRO2_SYSTEM_ID_{location}")
    }
    
    if not config['host'] or not config['db_path']:
        raise DatabaseError(f"未找到地点 {location} 的完整配置")
    
    return config

# 环境模式检测
APP_ENV = os.getenv("APP_ENV", "production")
if APP_ENV == "development":
    # 开发环境：Firebird使用SSH隧道
    USE_SSH_TUNNEL = True
    USE_FALLBACK = True
    USE_LOCAL_TESTING = False
elif APP_ENV == "local_testing":
    # 本地测试环境：使用模拟数据
    USE_SSH_TUNNEL = False
    USE_FALLBACK = False
    USE_LOCAL_TESTING = True
else:
    # 生产环境和其他环境：都直连
    USE_SSH_TUNNEL = False
    USE_FALLBACK = False
    USE_LOCAL_TESTING = False

class DatabaseError(Exception):
    """自定义数据库异常类"""
    pass

class SSHTunnelManager:
    """SSH隧道管理器"""
    
    def __init__(self):
        self.tunnel = None
        self.local_firebird_port = None
        self.is_connected = False
        self.use_fallback = False
        self.lock = threading.Lock()
    
    def create_tunnel(self, location: str = None):
        """创建SSH隧道（仅用于Firebird）"""
        if not USE_SSH_TUNNEL:
            logger.debug("[SSH_TUNNEL] SSH tunnel not required in current environment")
            return True
        
        # 获取当前地点的Firebird配置
        try:
            fb_config = get_firebird_config(location)
        except DatabaseError as e:
            if USE_FALLBACK:
                logger.warning(f"[SSH_TUNNEL] {e}, will use fallback connection")
                self.use_fallback = True
                return True
            else:
                raise
        
        # SSH隧道功能已禁用，因为这是专用的Firebird连接模块
        logger.info("[SSH_TUNNEL] SSH tunnel functionality disabled for dedicated Firebird module")
        return True
    
    def _test_tunnel_connectivity(self):
        """测试隧道连接性"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', self.local_firebird_port))
            sock.close()
            
            if result == 0:
                logger.debug("[SSH_TUNNEL] Firebird port test successful")
                return True
            else:
                logger.warning(f"[SSH_TUNNEL] Firebird port test failed: {result}")
                return False
                
        except Exception as e:
            logger.warning(f"[SSH_TUNNEL] Tunnel connectivity test failed: {e}")
            return False
    
    def close_tunnel(self):
        """关闭SSH隧道"""
        with self.lock:
            if self.tunnel:
                try:
                    self.tunnel.stop()
                    logger.debug("[SSH_TUNNEL] SSH tunnel closed")
                except:
                    pass
                finally:
                    self.tunnel = None
                    self.is_connected = False
                    self.local_firebird_port = None
    
    def get_connection_params(self, location: str = None):
        """获取连接参数"""
        try:
            fb_config = get_firebird_config(location)
            result = {
                'firebird_host': fb_config['host'],
                'firebird_port': fb_config['port']
            }
            
            logger.debug(f"[SSH_MANAGER] get_connection_params() 返回: {result}")
            return result
            
        except Exception as e:
            logger.error(f"[SSH_MANAGER] get_connection_params() 发生异常: {e}")
            raise DatabaseError(f"连接参数配置错误: {str(e)}")

# 全局SSH隧道管理器
_ssh_manager = None
_ssh_manager_lock = threading.Lock()

def get_ssh_manager():
    """获取SSH隧道管理器实例"""
    global _ssh_manager
    if _ssh_manager is None:
        with _ssh_manager_lock:
            if _ssh_manager is None:
                try:
                    _ssh_manager = SSHTunnelManager()
                    logger.debug("[GET_SSH_MANAGER] 成功创建 SSHTunnelManager 实例")
                except Exception as e:
                    logger.error(f"[GET_SSH_MANAGER] 创建 SSHTunnelManager 实例失败: {e}")
                    raise DatabaseError(f"无法创建SSH隧道管理器: {str(e)}")
    
    # 确保返回的是正确的实例类型
    if not isinstance(_ssh_manager, SSHTunnelManager):
        logger.error(f"[GET_SSH_MANAGER] 全局 _ssh_manager 变量类型错误: {type(_ssh_manager)}")
        with _ssh_manager_lock:
            _ssh_manager = None
            _ssh_manager = SSHTunnelManager()
            logger.warning("[GET_SSH_MANAGER] 重新创建了 SSHTunnelManager 实例")
    
    return _ssh_manager

class FirebirdConnectionPool:
    """优化的Firebird连接池"""
    
    def __init__(self, max_connections=25, min_connections=5, connection_timeout=60, location: str = None):
        logger.debug("[FB_POOL_INIT] Initializing FirebirdConnectionPool...")
        self.max_connections = max_connections
        self.min_connections = min_connections
        self.connection_timeout = connection_timeout
        self.pool = Queue()
        self.active_connections = 0
        self.lock = threading.RLock()
        self.location = location or LOCATION
        
        # 获取当前地点的Firebird配置
        self.fb_config = get_firebird_config(self.location)
        self.host = self.fb_config['host']
        self.port = self.fb_config['port']
        self.db_path = self.fb_config['db_path']
        self.successful_charset = None
        logger.debug(f"[FB_POOL_INIT] Location: {self.location}, Host: {self.host}, Port: {self.port}")
        
        # 初始化连接池
        logger.debug("[FB_POOL_INIT] Calling _initialize_pool().")
        self._initialize_pool()
        logger.debug("[FB_POOL_INIT] FirebirdConnectionPool initialization complete.")
    
    def _initialize_pool(self):
        """初始化连接池"""
        logger.debug("[FB_POOL_INIT_POOL] Entered _initialize_pool().")
        with self.lock:
            logger.debug(f"[FB_POOL_INIT_POOL] Lock acquired. Initializing {self.min_connections} connections.")
            for i in range(self.min_connections):
                logger.debug(f"[FB_POOL_INIT_POOL] Creating connection {i+1} of {self.min_connections}.")
                try:
                    conn = self._create_connection()
                    if conn:
                        self.pool.put(conn)
                        logger.debug(f"[FB_POOL_INIT_POOL] Connection {i+1} created and added to pool.")
                    else:
                        logger.warning(f"[FB_POOL_INIT_POOL] _create_connection() returned None for connection {i+1}.")
                except Exception as e:
                    logger.warning(f"[FB_POOL_INIT_POOL] Initialization failed for connection {i+1}: {e}")
    
    @retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
    def _create_connection(self):
        """创建新连接"""
        # 尝试不同的字符集，优先使用成功过的字符集
        charsets = ['UTF-8', 'GBK', 'WIN1252', 'ISO8859_1']
        if hasattr(self, 'successful_charset') and self.successful_charset:
            # 将成功的字符集放到首位
            charsets = [self.successful_charset] + [c for c in charsets if c != self.successful_charset]
        
        for charset_to_try in charsets:
            try:
                # 创建连接
                conn = fdb.connect(
                    host=self.host,
                    port=self.port,
                    database=self.db_path,
                    user=FIREBIRD_USER,
                    password=FIREBIRD_PASSWORD,
                    charset=charset_to_try
                )
                # 测试连接
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM RDB$DATABASE")
                    cursor.fetchone()
                
                self.successful_charset = charset_to_try
                with self.lock:
                    self.active_connections += 1
                return PooledConnection(self, conn)
                
            except DatabaseError:
                # 重新抛出数据库错误（如路径未找到）
                raise
            except Exception as e:
                logger.debug(f"[FB_POOL_CREATE_CONN] Connection attempt with charset {charset_to_try} failed: {e}")
                continue
        
        logger.error("[FB_POOL_CREATE_CONN] Failed to create connection with any charset.")
        raise DatabaseError("无法使用任何字符集创建连接")
    
    def get_connection(self):
        """获取连接"""
        try:
            # 尝试从池中获取
            conn = self.pool.get_nowait()
            
            # 验证连接是否有效
            if self._is_connection_valid(conn):
                return conn
            else:
                # 连接无效，创建新连接
                with self.lock:
                    self.active_connections -= 1
                return self._create_connection()
                
        except Empty:
            # 池中没有可用连接
            with self.lock:
                if self.active_connections < self.max_connections:
                    return self._create_connection()
            
            # 等待连接
            try:
                return self.pool.get(timeout=self.connection_timeout)
            except Empty:
                raise DatabaseError(f"无法在 {self.connection_timeout} 秒内获取连接")
    
    def return_connection(self, conn):
        """归还连接"""
        if isinstance(conn, PooledConnection):
            if self._is_connection_valid(conn._conn):
                self.pool.put(conn)
            else:
                with self.lock:
                    self.active_connections -= 1
    
    def _is_connection_valid(self, conn):
        """检查连接是否有效"""
        try:
            if isinstance(conn, PooledConnection):
                conn = conn._conn
            
            # 添加超时控制的连接检查
            with conn.cursor() as cursor:
                cursor.execute("SELECT FIRST 1 1 FROM RDB$DATABASE")
                result = cursor.fetchone()
                if result is None:
                    logger.warning("[FB_POOL_VALID] Connection test returned None")
                    return False
            return True
        except Exception as e:
            # 降低recv错误的日志级别，因为这是正常的连接池清理行为
            if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                logger.debug(f"[FB_POOL_VALID] Connection expired (normal cleanup): {e}")
            else:
                logger.warning(f"[FB_POOL_VALID] Connection validation failed: {e}")
            return False
    
    def cleanup_invalid_connections(self):
        """清理无效连接"""
        logger.debug("[FB_POOL_CLEANUP] Starting connection cleanup")
        invalid_connections = []
        valid_connections = []
        
        # 检查池中的所有连接
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                if self._is_connection_valid(conn):
                    valid_connections.append(conn)
                else:
                    invalid_connections.append(conn)
            except Empty:
                break
        
        # 关闭无效连接
        for conn in invalid_connections:
            try:
                if isinstance(conn, PooledConnection):
                    conn._conn.close()
                else:
                    conn.close()
                with self.lock:
                    self.active_connections -= 1
            except Exception as e:
                logger.warning(f"[FB_POOL_CLEANUP] Error closing invalid connection: {e}")
        
        # 将有效连接放回池中
        for conn in valid_connections:
            self.pool.put(conn)
        
        logger.info(f"[FB_POOL_CLEANUP] Cleaned up {len(invalid_connections)} invalid connections, {len(valid_connections)} valid")
    
    def close_all(self):
        """关闭所有连接"""
        logger.debug("[FB_POOL_CLOSE_ALL] Closing all connections")
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                if isinstance(conn, PooledConnection):
                    conn._conn.close()
                else:
                    conn.close()
            except Exception as e:
                logger.warning(f"[FB_POOL_CLOSE_ALL] Error closing connection: {e}")
        
        with self.lock:
            self.active_connections = 0
        logger.debug("[FB_POOL_CLOSE_ALL] All connections closed")

class PooledConnection:
    """池化连接包装器"""
    
    def __init__(self, pool, conn):
        self.pool = pool
        self._conn = conn
    
    def __enter__(self):
        return self._conn
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.pool.return_connection(self)
    
    def cursor(self):
        return self._conn.cursor()
    
    def commit(self):
        return self._conn.commit()
    
    def rollback(self):
        return self._conn.rollback()
    
    def close(self):
        """关闭连接时自动归还到池"""
        self.pool.return_connection(self)
    
    def __getattr__(self, name):
        """代理所有其他属性到实际连接"""
        return getattr(self._conn, name)

# 全局连接池实例
_firebird_pool = None
_pool_lock = threading.Lock()

def get_firebird_pool(location: str = None):
    """获取Firebird连接池实例"""
    global _firebird_pool
    logger.debug("[GET_FIREBIRD_POOL] Entered function.")
    if _firebird_pool is None:
        logger.debug("[GET_FIREBIRD_POOL] _firebird_pool is None. Attempting to acquire lock.")
        with _pool_lock:
            logger.debug("[GET_FIREBIRD_POOL] Lock acquired.")
            if _firebird_pool is None:
                logger.debug("[GET_FIREBIRD_POOL] _firebird_pool is still None. Initializing.")
                try:
                    logger.debug("[GET_FIREBIRD_POOL] Calling Config.get_config_dict().")
                    # config = Config.get_config_dict()
                    config = {  # 使用默认配置
                        'fb_pool_max_connections': 15,
                        'fb_pool_min_connections': 2,
                        'fb_pool_connection_request_timeout': 10
                    }
                    logger.debug(f"[GET_FIREBIRD_POOL] Config.get_config_dict() returned: {config}")
                    logger.debug("[GET_FIREBIRD_POOL] Initializing FirebirdConnectionPool.")
                    _firebird_pool = FirebirdConnectionPool(
                        max_connections=config.get('fb_pool_max_connections', 15),
                        min_connections=config.get('fb_pool_min_connections', 2),
                        connection_timeout=config.get('fb_pool_connection_request_timeout', 10),
                        location=location
                    )
                    logger.debug("[GET_FIREBIRD_POOL] FirebirdConnectionPool initialized.")
                except Exception as e:
                    logger.error(f"[GET_FIREBIRD_POOL] Exception during pool initialization: {e}", exc_info=True)
                    raise
            else:
                logger.debug("[GET_FIREBIRD_POOL] _firebird_pool was initialized by another thread.")
        logger.debug("[GET_FIREBIRD_POOL] Lock released.")
    else:
        logger.debug("[GET_FIREBIRD_POOL] _firebird_pool already initialized.")
    logger.debug("[GET_FIREBIRD_POOL] Returning _firebird_pool.")
    return _firebird_pool

@contextmanager
def get_pooled_pro2_connection(charset_preference: str = 'UTF-8', location: str = None):
    """获取池化的Firebird连接（上下文管理器）"""
    logger.debug(f"[GET_POOLED_PRO2_CONN] Attempting to get connection. Charset preference: {charset_preference}")
    pool = get_firebird_pool(location)
    conn = None
    try:
        logger.debug(f"[GET_POOLED_PRO2_CONN] Calling pool.get_connection(). Pool active: {pool.active_connections}, Max: {pool.max_connections}, Pool size: {pool.pool.qsize()}")
        conn = pool.get_connection()
        
        # 如果需要特定字符集且池中连接的字符集不匹配，创建新的专用连接
        if charset_preference != 'UTF-8' and charset_preference != pool.successful_charset:
            logger.debug(f"[GET_POOLED_PRO2_CONN] Pool connection charset ({pool.successful_charset}) doesn't match preference ({charset_preference}). Creating dedicated connection.")
            # 归还池连接
            if conn:
                conn.close()
            
            # 创建专用连接
            conn = create_dedicated_connection(charset_preference, location)
        
        if conn:
            logger.debug(f"[GET_POOLED_PRO2_CONN] Successfully got connection from pool. Yielding connection.")
        else:
            logger.error(f"[GET_POOLED_PRO2_CONN] pool.get_connection() returned None.")
        yield conn
    except Exception as e:
        logger.error(f"[GET_POOLED_PRO2_CONN] Exception during pool.get_connection() or yield: {e}", exc_info=True)
        raise
    finally:
        if conn:
            logger.debug(f"[GET_POOLED_PRO2_CONN] Closing connection in finally block.")
            if hasattr(conn, '_is_dedicated') and conn._is_dedicated:
                # 专用连接直接关闭
                conn._conn.close()
            else:
                # 池连接归还到池
                conn.close()
        else:
            logger.debug(f"[GET_POOLED_PRO2_CONN] No connection to close in finally block.")

@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def create_dedicated_connection(charset: str, location: str = None):
    """创建指定字符集的专用连接"""
    logger.debug(f"[CREATE_DEDICATED_CONN] Creating dedicated connection with charset: {charset}")
    
    try:
        fb_config = get_firebird_config(location)
        host = fb_config['host']
        port = fb_config['port']
        db_path = fb_config['db_path']
        
        logger.debug(f"[CREATE_DEDICATED_CONN] Connecting with charset: {charset}")
        
        # 创建连接
        conn = fdb.connect(
            host=host,
            port=port,
            database=db_path,
            user=FIREBIRD_USER,
            password=FIREBIRD_PASSWORD,
            charset=charset
        )
        
        # 测试连接
        with conn.cursor() as cursor:
            cursor.execute("SELECT FIRST 1 1 FROM RDB$DATABASE")
            cursor.fetchone()
        
        logger.debug(f"[CREATE_DEDICATED_CONN] Dedicated connection created successfully with charset: {charset}")
        
        # 创建包装器，标记为专用连接
        wrapper = DedicatedConnection(conn)
        return wrapper
        
    except Exception as e:
        logger.error(f"[CREATE_DEDICATED_CONN] Failed to create dedicated connection with charset {charset}: {e}")
        
        # 如果是非UTF-8字符集失败，尝试UTF-8回退
        if charset != 'UTF-8':
            logger.warning(f"[CREATE_DEDICATED_CONN] Retrying with UTF-8 charset as fallback")
            try:
                fb_config = get_firebird_config(location)
                conn = fdb.connect(
                    host=fb_config['host'],
                    port=fb_config['port'],
                    database=fb_config['db_path'],
                    user=FIREBIRD_USER,
                    password=FIREBIRD_PASSWORD,
                    charset='UTF-8'
                )
                
                # 测试连接
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM RDB$DATABASE")
                    cursor.fetchone()
                
                logger.warning(f"[CREATE_DEDICATED_CONN] UTF-8 fallback successful")
                wrapper = DedicatedConnection(conn)
                return wrapper
                
            except Exception as fallback_e:
                logger.error(f"[CREATE_DEDICATED_CONN] UTF-8 fallback also failed: {fallback_e}")
        
        raise DatabaseError(f"无法创建专用连接，字符集: {charset}, 错误: {str(e)}")

class DedicatedConnection:
    """专用连接包装器"""
    
    def __init__(self, conn):
        self._conn = conn
        self._is_dedicated = True
    
    def cursor(self):
        return self._conn.cursor()
    
    def commit(self):
        return self._conn.commit()
    
    def rollback(self):
        return self._conn.rollback()
    
    def close(self):
        """专用连接直接关闭，不归还到池"""
        if self._conn:
            self._conn.close()
    
    def __getattr__(self, name):
        """代理所有其他属性到实际连接"""
        return getattr(self._conn, name)




def execute_pro2_query(
    query: str, 
    params: tuple = None, 
    fetch_all: bool = False, 
    charset: str = 'UTF-8',
    location: str = None
) -> Union[Tuple, List[Tuple], None]:
    """执行Firebird查询"""
    # 本地测试模式：返回模拟数据
    if USE_LOCAL_TESTING:
        logger.info(f"[LOCAL_TESTING] Mock Firebird query execution: {query[:100]}...")
        return _get_mock_pro2_data(query, fetch_all)
    
    with get_pooled_pro2_connection(charset_preference=charset, location=location) as conn:
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            if query.strip().upper().startswith(("INSERT", "UPDATE", "DELETE")):
                conn.commit()
                return cursor.rowcount
            elif fetch_all:
                return cursor.fetchall()
            else:
                return cursor.fetchone()

def _get_mock_pro2_data(query: str, fetch_all: bool = False):
    """返回模拟的Firebird数据"""
    query_upper = query.upper()
    
    # 根据查询类型返回不同的模拟数据
    if "USERS" in query_upper:
        if fetch_all:
            return [
                (1, "testuser", "Test User", "测试部门"),
                (2, "admin", "Admin User", "管理部门")
            ]
        else:
            return (1, "testuser", "Test User", "测试部门")
    
    elif "JOB" in query_upper or "BOOKING" in query_upper:
        if fetch_all:
            return [
                ("JOB001", 1, "测试客户", "2025-05-01", 1000.0, "测试操作员"),
                ("JOB002", 2, "测试客户2", "2025-05-02", 2000.0, "测试操作员2")
            ]
        else:
            return ("JOB001", 1, "测试客户", "2025-05-01", 1000.0, "测试操作员")
    
    elif "PROFIT" in query_upper:
        if fetch_all:
            return [
                (1000.0, 800.0, 200.0),
                (2000.0, 1500.0, 500.0)
            ]
        else:
            return (1000.0, 800.0, 200.0)
    
    else:
        # 默认返回空结果
        if fetch_all:
            return []
        else:
            return None

# 异步支持函数（向后兼容）
import asyncio
from contextlib import asynccontextmanager

async def execute_pro2_query_async(query: str, params: tuple = None, fetch_all: bool = False, charset: str = 'UTF-8', location: str = None):
    """异步执行Firebird查询"""
    return await asyncio.to_thread(execute_pro2_query, query, params, fetch_all, charset, location)



# 清理函数
def cleanup_invalid_connections_periodically():
    """定期清理无效连接（保持连接池活跃）"""
    global _firebird_pool
    
    if _firebird_pool:
        try:
            _firebird_pool.cleanup_invalid_connections()
            logger.debug("[PERIODIC_CLEANUP] Firebird connection pool cleaned")
        except Exception as e:
            logger.error(f"[PERIODIC_CLEANUP] Error during periodic cleanup: {e}")

def cleanup_connections():
    """清理所有连接"""
    global _firebird_pool, _ssh_manager
    
    logger.info("[CLEANUP] Starting connection cleanup")
    
    # 清理Firebird连接池
    if _firebird_pool:
        try:
            _firebird_pool.close_all()
            logger.info("[CLEANUP] Firebird connection pool closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing Firebird pool: {e}")
        finally:
            _firebird_pool = None
    
    # 清理SSH隧道
    if _ssh_manager:
        try:
            _ssh_manager.close_tunnel()
            logger.info("[CLEANUP] SSH tunnel closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing SSH tunnel: {e}")
        finally:
            _ssh_manager = None
    
    logger.info("[CLEANUP] Connection cleanup completed")

# 安全格式化日期
def safe_format_date(date_value, format_str='%Y-%m-%d'):
    """安全格式化日期，处理各种日期类型"""
    import pandas as pd
    from datetime import datetime, date
    
    if date_value is None or pd.isna(date_value):
        return None
    
    # 如果已经是字符串，直接返回（假设已经是正确格式）
    if isinstance(date_value, str):
        # 尝试解析字符串日期并重新格式化
        try:
            # 常见的日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d', 
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S'
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_value.strip(), fmt)
                    return parsed_date.strftime(format_str)
                except ValueError:
                    continue
            
            # 如果无法解析，返回原字符串
            return date_value
        except:
            return date_value
    
    # 如果是datetime或date对象
    if isinstance(date_value, (datetime, date)):
        try:
            return date_value.strftime(format_str)
        except:
            return str(date_value)
    
    # 如果是pandas的Timestamp
    if hasattr(date_value, 'strftime'):
        try:
            return date_value.strftime(format_str)
        except:
            return str(date_value)
    
    # 其他情况，尝试转换为字符串
    return str(date_value) if date_value else None

import atexit
atexit.register(cleanup_connections)

# 导出常用函数
__all__ = [
    'DatabaseError',
    'get_firebird_config',
    'get_pooled_pro2_connection',
    'execute_pro2_query',
    'execute_pro2_query_async',
    'cleanup_connections',
    'LOCATION'
]